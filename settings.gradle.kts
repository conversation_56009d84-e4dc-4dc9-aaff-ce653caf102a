pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        jcenter()
        google()
        mavenCentral()
        maven { setUrl("https://jitpack.io") }
    }
}


rootProject.name = "Camera GPS"
include(":app")
include(":commonRes")
include(":permissionx")
include(":toggle")
include(":cameraview")
//include(":ffmpeg-kit-android-lib")
include(":gpuimage")
include(":gpuv")
